
-----

### **Epic: GUA-A - Core Analysis Engine & Backend Service**

**Goal:** To establish a backend service capable of receiving a chess position (FEN), analyzing it with a native Stockfish engine, and returning detailed analysis results. [cite\_start]This forms the analytical foundation for the entire system[cite: 17, 226].

| Ticket ID | Title |
| :--- | :--- |
| **GUA-01** | **Setup Backend Project & UCI Wrapper** |
| | [cite\_start]**Background:** The system's value proposition depends on high-quality analysis, which necessitates a backend architecture to run a native Stockfish engine for maximum performance[cite: 175]. This ticket creates the foundation for that service and the communication layer with the engine. [cite\_start]The standard for this communication is the Universal Chess Interface (UCI) protocol[cite: 154, 307]. |
| | **Acceptance Criteria:** \<br\> - A new Python project is initialized for the backend service. [cite\_start]\<br\> - The service can successfully launch a local Stockfish executable as a sandboxed subprocess[cite: 306]. [cite\_start]\<br\> - A wrapper module can send the `uci` command to the engine and correctly parse the `uciok` response[cite: 158]. \<br\> - The service exposes a basic health check endpoint (e.g., `/health`). |
| | **Implementation Suggestions:** Consider a lightweight Python web framework like Flask or FastAPI. [cite\_start]The `python-chess` library provides a robust, high-level UCI wrapper that handles engine process management and communication, and is the recommended tool[cite: 162]. |
| **GUA-02** | **Implement FEN Position Analysis Endpoint** |
| | **Background:** The system needs a standard way to request analysis for a given board state. [cite\_start]The Forsyth-Edwards Notation (FEN) is the standard for representing a board state in a single line of text[cite: 47, 311]. This ticket creates the API endpoint that will be the primary entry point for all analysis requests from any client. |
| | **Acceptance Criteria:** \<br\> - A new API endpoint (e.g., `POST /analyze`) is created that accepts a FEN string in the request body. [cite\_start]\<br\> - The endpoint uses the UCI wrapper to send the `position fen <fenstring>` command to the Stockfish process[cite: 159, 311]. [cite\_start]\<br\> - The endpoint sends a `go movetime <ms>` command to initiate a time-limited search[cite: 160, 312]. [cite\_start]\<br\> - The endpoint successfully parses the final `bestmove` response from the engine and returns it in the API response[cite: 161]. |
| | [cite\_start]**Implementation Suggestions:** The `python-chess` library should be used to validate the incoming FEN string before passing it to the engine[cite: 47]. The analysis time (movetime) should be a configurable parameter in the request. |
| **GUA-03** | **Implement Multi-PV Analysis and Info Parsing** |
| | [cite\_start]**Background:** To identify critical moments and hidden tactics, the system must analyze not just the single best move, but the top N best moves[cite: 183]. [cite\_start]This is achieved using the engine's Multi-PV (Multi-Principal Variation) feature[cite: 183]. [cite\_start]The engine provides this rich data via a stream of `info` strings during its search[cite: 161]. |
| | [cite\_start]**Acceptance Criteria:** \<br\> - The analysis endpoint can be configured to set the `MultiPV` option on the Stockfish engine (e.g., `MultiPV 5`) via a UCI command[cite: 185]. [cite\_start]\<br\> - The UCI wrapper can parse the continuous stream of `info` strings from the engine while it is searching[cite: 161]. [cite\_start]\<br\> - The parsed `info` data (including depth, score in centipawns, and the principal variation `pv`) for each of the N variations is collected[cite: 161, 313]. \<br\> - The final API response includes the detailed analysis for all N variations, not just the single best move. |
| | [cite\_start]**Implementation Suggestions:** The `python-chess` UCI wrapper has built-in capabilities for parsing `info` strings into convenient objects[cite: 162]. The number of variations (N) should be a configurable parameter passed to the analysis endpoint. |

-----

### **Epic: GUA-B - Lichess API Integration**

[cite\_start]**Goal:** To connect the system to the Lichess platform, allowing it to authenticate on behalf of a user and receive real-time game state information from their live games[cite: 228].

| Ticket ID | Title |
| :--- | :--- |
| **GUA-04** | **Implement Lichess API Client & PAT Authentication** |
| | [cite\_start]**Background:** To integrate with Lichess, the system must act as a client to its official API[cite: 23]. [cite\_start]For the initial MVP, authentication will be handled using a user-provided Personal Access Token (PAT), which is a straightforward method ideal for development and initial rollout[cite: 40, 44]. |
| | **Acceptance Criteria:** \<br\> - A new service module for Lichess integration is created. \<br\> - The system can securely accept and store a Lichess PAT provided by the user. [cite\_start]\<br\> - The client includes the PAT as a Bearer token in the `Authorization` header of all API requests[cite: 40]. \<br\> - The client can make an authenticated request to a basic Lichess API endpoint (e.g., `/api/account`) and successfully parse the response. |
| | [cite\_start]**Implementation Suggestions:** The officially-endorsed `berserk` Python library is the recommended client for its feature completeness and active maintenance[cite: 9, 54]. |
| **GUA-05** | **Stream Global Events to Detect Game Start** |
| | **Background:** To know when to start monitoring a game, the application must listen to a general event stream. [cite\_start]The `GET /api/stream/event` endpoint provides a stream of key events, including `gameStart`, which contains the `gameId` needed to connect to a specific game[cite: 7, 28]. |
| | [cite\_start]**Acceptance Criteria:** \<br\> - The Lichess client can open a persistent streaming connection to the `/api/stream/event` endpoint[cite: 27]. [cite\_start]\<br\> - The client can correctly parse the Newline Delimited JSON (ND-JSON) stream[cite: 25]. \<br\> - The client logic can identify `gameStart` events from the stream and successfully extract the `gameId`. \<br\> - The extracted `gameId` is passed to a queue or callback for the next module to handle. |
| | [cite\_start]**Implementation Suggestions:** The `berserk` library has built-in support for handling ND-JSON streams[cite: 52]. [cite\_start]This module must be designed to be resilient to network drops and capable of reconnecting automatically[cite: 66, 67]. |
| **GUA-06** | **Stream Real-Time Game State and Maintain Board** |
| | [cite\_start]**Background:** Once a `gameId` is known, the system must connect to that specific game stream to receive move-by-move updates[cite: 29]. [cite\_start]The `GET /api/board/game/stream/{gameId}` endpoint provides this functionality, starting with a full game state object and then sending subsequent `gameState` updates for each move[cite: 30, 31]. |
| | [cite\_start]**Acceptance Criteria:** \<br\> - Given a `gameId`, the Lichess client connects to the `/api/board/game/stream/{gameId}` endpoint[cite: 29]. [cite\_start]\<br\> - The client correctly parses the initial `gameFull` object and initializes an internal board representation with the starting FEN[cite: 30]. [cite\_start]\<br\> - For each subsequent `gameState` object, the client extracts the move in UCI notation[cite: 31]. \<br\> - The UCI move is applied to the internal board representation, which is then used to generate an updated FEN string. |
| | **Implementation Suggestions:** Use the `python-chess` library to manage the internal board state. [cite\_start]A `chess.Board` object can be initialized with the starting FEN and then have moves `push`ed onto it in UCI format to keep it perfectly synchronized[cite: 50, 480]. |

-----

### **Epic: GUA-C - "Tutor Brain" Critical Moment Logic**

[cite\_start]**Goal:** To implement the core pedagogical algorithms that analyze engine output and decide when a "Critical Moment" has occurred, triggering a suggestion[cite: 346, 386].

| Ticket ID | Title |
| :--- | :--- |
| **GUA-07**| **Implement Tactic Finder / Critical Point Algorithm** |
| | [cite\_start]**Background:** The system must proactively identify moments of high opportunity for the user[cite: 399]. [cite\_start]This pre-move analysis checks if the current position contains a significantly better move compared to alternatives, indicating a critical tactical moment[cite: 191, 401]. |
| | **Acceptance Criteria:** \<br\> - A function takes a FEN string and the Multi-PV analysis results as input. [cite\_start]\<br\> - It calculates the evaluation gap between the best move (E1) and the second-best move (E2)[cite: 192]. [cite\_start]\<br\> - If `E1 - E2` is greater than a configurable `Ttactic` threshold (e.g., 150 centipawns), the function returns `True`[cite: 192, 215]. \<br\> - Otherwise, it returns `False`. |
| | **Implementation Suggestions:** This logic should be encapsulated in its own module. The `Ttactic` threshold should be easily tunable based on testing. |
| **GUA-08**| **Implement Post-Move Blunder Detection Algorithm** |
| | [cite\_start]**Background:** The system must also react to user mistakes[cite: 195]. [cite\_start]This involves comparing the move the user played against the best move that was available to determine the "centipawn loss" (CPL) and flag it as a blunder if it exceeds a threshold[cite: 206, 207]. |
| | **Acceptance Criteria:** \<br\> - A function receives the analysis of the position *before* the user's move (containing E\_best) and the user's chosen move. [cite\_start]\<br\> - It calculates the Centipawn Loss (CPL) by comparing the evaluation of the best move with the evaluation of the user's move[cite: 206]. [cite\_start]\<br\> - If the CPL exceeds a configurable `Tblunder` threshold (e.g., 200 centipawns), the function flags it as a potential blunder[cite: 216]. \<br\> - The function returns the CPL value and the suggested best move. |
| | **Implementation Suggestions:** To get the evaluation of the user's specific move, you may need to perform a quick, shallow search on that move alone or find it within the initial Multi-PV analysis. |
| **GUA-09**| **Implement Contextual State-Change Filter** |
| | [cite\_start]**Background:** Raw centipawn loss alone is not enough; a 200cp drop from a +10 position is meaningless[cite: 214]. [cite\_start]True blunders are those that change the expected outcome of the game (e.g., winning to drawn)[cite: 214, 395]. This ticket adds that critical layer of context. |
| | [cite\_start]**Acceptance Criteria:** \<br\> - A helper function is created that classifies an evaluation score into a game state ("Winning," "Equal," "Losing") based on configurable ranges (e.g., `|eval| < 0.5` is Equal)[cite: 217]. \<br\> - The Blunder Detection logic from GUA-08 is updated. [cite\_start]\<br\> - A move is only flagged as a true blunder if its CPL is above the threshold AND it causes a negative change in game state (e.g., from "Winning" to "Equal")[cite: 217]. |
| | **Implementation Suggestions:** The state thresholds (e.g., what constitutes a "winning" advantage) should be easily configurable to allow for tuning based on the target user's skill level. |

-----

### **Epic: GUA-D - MVP Client with Real-Time Textual Guidance**

**Goal:** To build a functional, stable desktop client that automatically connects to our backend, provides real-time status feedback, receives live guidance from the Tutor Brain, and clearly instructs the user whose move it is using standard chess notation.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-10** | **Setup Desktop Client with Real-Time Status Display** |
| | **Background:** This ticket establishes the foundation for our new client. We will build a standard desktop window with a clean layout designed for automatic status updates and to display text-based instructions clearly to the user, replacing the need for a manual connection check. |
| | **Acceptance Criteria:** \<br\> - A new project directory `guardian-client` is initialized with Poetry, `PySide6`, and `requests`. \<br\> - The application launches a standard, non-overlay desktop window. \<br\> - The UI contains a dedicated status indicator widget (e.g., a colored circle and a `QLabel` with text "Status: Connecting..."). \<br\> - On startup, the client attempts a single HTTP request to the backend's `/health` endpoint to verify it's running and updates the status indicator to "Disconnected" or "Connected" based on the result. |
| | **Implementation Suggestions:** Use `PySide6` for the UI. The initial status check can be done in a separate thread on startup to prevent blocking the UI. The status indicator could be a simple custom widget that manages its color and text. |
| **GUA-11** | **Implement Real-Time WebSocket and Live Status Updates** |
| | **Background:** To receive live instructions and provide true real-time connection status, the client needs a persistent WebSocket connection. This ticket implements that connection and uses the WebSocket's own lifecycle events to drive the UI's status indicator. |
| | **Acceptance Criteria:** \<br\> - The `guardian-engine` backend is updated with a WebSocket endpoint (e.g., using `Flask-SocketIO`). \<br\> - The `guardian-client` connects to this WebSocket endpoint automatically after a successful initial health check. \<br\> - Upon a successful WebSocket `connect` event, the UI's status indicator immediately updates to show "Status: Connected" (e.g., with a green color). \<br\> - Upon a `disconnect` or `connect_error` event, the status indicator immediately updates to show "Status: Disconnected" (e.g., with a red color), and the client should attempt to reconnect automatically. \<br\> - The client can successfully receive custom events (e.g., `critical_moment`) from the backend and log them. |
| | **Implementation Suggestions:** Use `Flask-SocketIO` on the backend and `python-socketio` on the client. The client's `sio.on('connect', ...)` and `sio.on('disconnect', ...)` event handlers are the perfect place to implement the UI status updates. |
| **GUA-12** | **Implement State Machine and Instructional Display** |
| | **Background:** This ticket implements the core user-facing logic. Based on the events received from the live WebSocket connection, the client will manage its internal state ("Analyzing," "User Move," "Guardian Move") and update the text display to guide the user. |
| | **Acceptance Criteria:** \<br\> - The client maintains a state machine initialized to `'ANALYZING'`. The UI initially displays "Analyzing position...". \<br\> - When a `critical_moment` or `blunder_detected` event is received via WebSocket, the state switches to `GUARDIAN_MOVE`, and the UI updates to show: **Instruction Label:** "Guardian's Move: Play", **Suggestion Label:** "Qxh7+". \<br\> - When a `quiet_move` event is received, the state switches to `USER_MOVE`, and the UI updates to show: **Instruction Label:** "Your Move", **Suggestion Label:** is cleared. |
| | **Implementation Suggestions:** A simple variable or an enum in the main window class can manage the state. The WebSocket event handlers for our custom events will be responsible for updating this state and calling a central `update_ui()` function that changes the label texts based on the current state. |

-----

### **Epic: GUA-E - Browser Extension Foundation & Integration**

**Goal:** To establish a robust Manifest V3 browser extension that serves as the bridge between the user's live game on Chess.com and the existing backend analysis engine.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-13** | **Setup Manifest V3 Extension Skeleton** |
| | **Background:** The first step for Phase 2 is creating the fundamental structure of the browser extension. This involves setting up the manifest file, which defines the extension's properties and permissions, and creating the initial script files according to the modern Manifest V3 standard. |
| | **Acceptance Criteria:** \<br\> - A `manifest.json` file is created and configured for Manifest V3. \<br\> - The manifest correctly declares necessary permissions (e.g., `storage`). \<br\> - A background service worker is registered in the manifest. \<br\> - A content script is configured to be injected specifically into Chess.com live game URLs (e.g., `https://www.chess.com/game/live/*`). \<br\> - The extension can be loaded into a Chromium browser in developer mode without any manifest errors. |
| | **Implementation Suggestions:** Use standard JavaScript for the service worker and content script. Ensure the `host_permissions` in the manifest are strictly limited to `*.chess.com` to adhere to the principle of least privilege. |
| **GUA-14** | **Establish Content-to-Background Communication Channel** |
| | **Background:** The content script (which will read the board) and the background service worker (which will handle external communication) operate in different contexts. A reliable messaging channel between them is essential for passing game state data. |
| | **Acceptance Criteria:** \<br\> - The content script can successfully send a message containing a mock FEN string to the background service worker using the `chrome.runtime.sendMessage` API. \<br\> - The background service worker has an `onMessage` listener that correctly receives the message from the content script. \<br\> - Data integrity is maintained; the received data in the background worker is identical to what was sent from the content script. |
| | **Implementation Suggestions:** Utilize the standard `chrome.runtime` messaging APIs. This ticket focuses solely on establishing the channel, not the content of the messages, which will be handled in later tickets. |
| **GUA-15** | **Connect Background Worker to Phase 1 Backend Service** |
| | **Background:** The background service worker acts as a secure proxy, forwarding analysis requests to the backend engine built in Phase 1. It must be able to make network requests to this external service. |
| | **Acceptance Criteria:** \<br\> - The background service worker can make a successful `fetch` request to the backend's `/health` endpoint to verify connectivity. \<br\> - The worker can make a `POST` request to the backend's `/analyze` endpoint, sending a mock FEN string and receiving the analysis JSON in response. \<br\> - The backend's CORS policy is correctly configured to accept requests originating from the browser extension's unique ID. |
| | **Implementation Suggestions:** Use the standard `fetch` API. The backend service address should be a configurable setting within the extension, perhaps managed via `chrome.storage`. |

-----

### **Epic: GUA-F - Real-Time DOM Scraping & Game State Reconstruction**

**Goal:** To reliably read the live game state from the Chess.com web page by observing DOM changes and reconstructing the board state in real-time. This is the most technically challenging and fragile part of Phase 2.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-16** | **Implement Resilient Selector for Move List Container** |
| | **Background:** Relying on obfuscated, auto-generated CSS class names to find the move list is not viable as they change with every website update. This ticket is dedicated to creating a robust selector strategy that can find the correct DOM element even when class names change. |
| | **Acceptance Criteria:** \<br\> - The content script contains a function that successfully locates and returns the DOM element that contains the game's move notation. \<br\> - The selection logic does not rely on a single, fragile class name. \<br\> - The selector works correctly on different Chess.com board layouts (e.g., standard, focus mode). |
| | **Implementation Suggestions:** Investigate using a combination of ARIA roles (`role="log"`), stable `data-` attributes, or unique element structures (e.g., "find the element that contains a series of `div`s with specific child nodes"). |
| **GUA-17** | **Implement MutationObserver for Real-Time Move Detection** |
| | **Background:** To provide real-time analysis, we must detect new moves the instant they appear on the page. The `MutationObserver` API is the modern, efficient way to watch for DOM changes without resorting to inefficient polling. |
| | **Acceptance Criteria:** \<br\> - A `MutationObserver` is instantiated and attached to the move list container element found in GUA-16. \<br\> - The observer is configured to watch for `childList` mutations (i.e., new nodes being added). \<br\> - When a new move notation element is added to the DOM, the observer's callback function is triggered successfully. \<br\> - The callback function correctly filters out irrelevant DOM changes and extracts only the text of the new move (e.g., "e4", "Nf3"). |
| | **Implementation Suggestions:** The callback logic must be defensive, ensuring it only processes nodes that match the expected structure of a move element. |
| **GUA-18** | **Reconstruct and Maintain Board State (FEN)** |
| | **Background:** Once a new move is detected, the extension needs to update its internal representation of the board to generate a correct, up-to-date FEN string for analysis. This ticket implements that game logic within the content script. |
| | **Acceptance Criteria:** \<br\> - The content script initializes a `chess.js` instance when the game page loads, correctly setting up the starting position. \<br\> - When a new move is detected by the `MutationObserver`, its SAN (Standard Algebraic Notation) is applied to the `chess.js` board object. \<br\> - After applying the move, the script generates a valid FEN string from the `chess.js` object. \<br\> - The generated FEN is then sent to the background service worker via the communication channel from GUA-14. |
| | **Implementation Suggestions:** The `chess.js` library will need to be bundled with the extension's assets. The script must handle initial page load by parsing all existing moves in the list to build the starting FEN for that session. |
| **GUA-19** | **Implement Ethical Use & Risk Disclaimer** |
| | **Background:** As outlined in the PRD and research, using a DOM-scraping tool on Chess.com carries risks related to their Terms of Service and Fair Play Policy. It is our ethical responsibility to inform the user clearly. |
| | **Acceptance Criteria:** \<br\> - On the first run of the extension on a game page, a modal dialog is displayed to the user. \<br\> - The dialog clearly explains that the tool is for learning, is not affiliated with Chess.com, and that use of third-party tools may carry risks. \<br\> - The user must click an "I Understand and Accept" button to dismiss the dialog. \<br\> - The user's acceptance is stored in `chrome.storage` so the dialog does not appear on subsequent uses. |
| | **Implementation Suggestions:** The modal can be a simple HTML/CSS/JS component injected into the page by the content script. The text content must be reviewed and approved by the product lead. |

-----

### **Epic: GUA-J - Browser Extension UI & Suggestion Display**

**Goal:** To create a non-intrusive UI element within the browser extension that displays the real-time, text-based guidance from the Tutor Brain directly on the Chess.com page.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-20** | **Create UI Panel for Guardian Suggestions** |
| | **Background:** Our extension needs a dedicated space on the Chess.com page to display information. This ticket involves creating a floating `div` element that will serve as our main UI panel. It needs to be styled so it's helpful but not intrusive. |
| | **Acceptance Criteria:** \<br\> - When `content.js` loads, it dynamically creates a new `div` element and injects it into the page's `<body>`. \<br\> - This `div` is styled to be a "floating panel" (e.g., using `position: fixed;`, a high `z-index`, and placed in a corner like the top-right). \<br\> - The panel contains the three necessary `QLabel`-like elements we planned for the desktop client: a main instruction label, a suggestion label, and an "Opponent's Last Move" label. \<br\> - The panel is initially visible with a "Connecting..." message. |
| | **Implementation Suggestions:** The `content.js` script will be responsible for all DOM creation and styling. It's best to give the panel a unique ID (e.g., `guardian-tutor-panel`) to avoid conflicts with Chess.com's own styles. |
| **GUA-21** | **Establish Backend-to-UI Communication Channel** |
| | **Background:** The analysis events from our backend arrive in the `background.js` service worker. We need to forward these events to the `content.js` script that controls the UI panel on the active tab. |
| | **Acceptance Criteria:** \<br\> - In `background.js`, when a Tutor Brain event is received from the WebSocket, it identifies the active tab where the game is being played. \<br\> - It uses `chrome.tabs.sendMessage` to send the complete, formatted suggestion data to that specific tab's `content.js`. \<br\> - In `content.js`, a `chrome.runtime.onMessage` listener is implemented to receive these suggestion events from the background script. \<br\> - When a message is received, it is logged to the page's console to verify the channel is working. |
| | **Implementation Suggestions:** The message passing between background and content scripts is a core feature of browser extensions. This creates a secure bridge, ensuring the content script only reacts to events from our own extension. |
| **GUA-22** | **Implement UI State Machine and Display Logic** |
| | **Background:** This is the final step where we connect the data to the display. The UI panel needs to react to the incoming events, update its internal state, and display the correct instructions to the user, just like our desktop client does. |
| | **Acceptance Criteria:** \<br\> - The `content.js` script implements the full, turn-aware state machine: `ANALYZING`, `USER_TURN_GUARDIAN`, `USER_TURN_QUIET`, `OPPONENT_TURN`. \<br\> - The `onMessage` listener from GUA-21 now triggers the state machine logic. \<br\> - Based on the state, a central `update_ui()` function updates the text and visibility of the labels in the floating UI panel. \<br\> - The UI correctly displays "Waiting for opponent...", "Your Move", or "Guardian's Move: Play [move]" at the appropriate times. |
| | **Implementation Suggestions:** This logic will be very similar to what was implemented in the Python desktop client, but now written in JavaScript within the `content.js` file. |

-----

### **Epic: GUA-G - Desktop Application Shell & Vision Foundation**

**Goal:** To build the foundational cross-platform desktop application using Electron.js that will house the CV logic, manage screen capture, and provide the user with necessary setup and calibration tools.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-23** | **Setup Electron Application & IPC Foundation** |
| | **Background:** Phase 3 requires a standalone desktop application to perform screen capture. Electron.js is the chosen framework for its cross-platform capabilities using web technologies. This ticket establishes the basic project structure. |
| | **Acceptance Criteria:** \<br\> - A new Electron.js project is initialized with a main process and a renderer process. \<br\> - The application launches into a basic window displaying a placeholder UI. \<br\> - A robust Inter-Process Communication (IPC) channel is established between the main process (which will handle system-level tasks like capture) and the renderer process (UI). \<br\> - The project is configured with a bundler (e.g., Webpack, Vite) for efficient asset management. |
| | **Implementation Suggestions:** Use the standard `ipcMain` and `ipcRenderer` modules for communication. The main process should be written in Node.js, and the renderer process in standard HTML/CSS/JS. |
| **GUA-24** | **Implement Screen Region Selection & Calibration UI** |
| | **Background:** The application cannot assume where the chessboard is on the user's screen. A one-time calibration step is required where the user selects the screen region that the vision pipeline should monitor. |
| | **Acceptance Criteria:** \<br\> - The application UI includes a "Calibrate Position" button. \<br\> - When clicked, the application enters a calibration mode (e.g., a semi-transparent overlay on the whole screen). \<br\> - The user can click and drag to draw a rectangle over the target chessboard. \<br\> - The screen coordinates (x, y, width, height) of the selected region are saved persistently to local storage. |
| | **Implementation Suggestions:** The `desktopCapturer` API in Electron can be used to get screen sources. The calibration UI can be a simple borderless window. |
| **GUA-25** | **Implement Periodic Screen Capture of Selected Region** |
| | **Background:** Once calibrated, the core function of the vision foundation is to continuously capture images of the specified screen area. This stream of images will be the input for the CV pipeline. |
| | **Acceptance Criteria:** \<br\> - The application, running in the background, captures an image of the saved screen region at a regular interval (e.g., every 1-2 seconds). \<br\> - The capture process is efficient and has minimal impact on system performance. \<br\> - The captured image data (e.g., as a Buffer or base64 string) is passed from the main process to the renderer process via IPC for further processing. |
| | **Implementation Suggestions:** Use a library like `screen_capture_lite` or a similar Node.js module that is known to be performant. The capture interval should be configurable. |

-----

### **Epic: GUA-H - OpenCV Pipeline for Board Detection**

**Goal:** To implement the traditional computer vision algorithms that take a raw screenshot and reliably locate, isolate, and rectify the chessboard into a clean, square, 8x8 grid suitable for piece recognition.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-26** | **Implement Board Localization with Contour Detection** |
| | **Background:** The first step in the CV pipeline is to find the chessboard within the captured image. This involves a series of image processing steps to identify the board's four corners. |
| | **Acceptance Criteria:** \<br\> - A function is created that takes a raw image buffer as input. \<br\> - The image is preprocessed (converted to grayscale, blurred). \<br\> - Edge detection (e.g., Canny) is applied. \<br\> - Contour detection is used to find the largest quadrilateral shape in the image. \<br\> - The function returns the coordinates of the four detected corners of the chessboard. |
| | **Implementation Suggestions:** Use `OpenCV.js`, the JavaScript port of OpenCV, to perform these operations within the Electron renderer process. |
| **GUA-27** | **Implement Perspective Transformation (Unwarping)** |
| | **Background:** The detected board might be skewed due to the user's viewing angle. To prepare for piece recognition, we must transform this skewed view into a perfect top-down square. |
| | **Acceptance Criteria:** \<br\> - A function takes the source image and the four corner coordinates (from GUA-23) as input. \<br\> - It computes a perspective transformation matrix. \<br\> - It applies the transformation (`warpPerspective`) to the image. \<br\> - The function outputs a new image that is a perfectly square, unwarped representation of the chessboard. |
| | **Implementation Suggestions:** This is a standard `OpenCV.js` operation. The output image should be a fixed size (e.g., 512x512 pixels) to standardize the input for the next stage. |
| **GUA-28** | **Implement Square Segmentation** |
| | **Background:** After unwarping the board into a perfect square, the final step of this stage is to slice it into 64 individual images, one for each square. |
| | **Acceptance Criteria:** \<br\> - A function takes the unwarped square board image as input. \<br\> - It mathematically divides the image into an 8x8 grid. \<br\> - The function returns an array or matrix containing 64 smaller image objects, each representing a single square. |
| | **Implementation Suggestions:** This is a straightforward image-slicing operation. The output array of 64 images will be the direct input for the CNN model in the next epic. |

-----

### **Epic: GUA-I - CNN Piece Recognition & Final Integration**

**Goal:** To build, train, and integrate a machine learning model for accurate piece recognition and to assemble the final FEN string, connecting the vision app to the backend analysis service.

| Ticket ID | Title |
| :--- | :--- |
| **GUA-29** | **Collect, Augment, and Label Piece Image Dataset** |
| | **Background:** The performance of the entire vision system hinges on the quality of the training data for the piece recognition model. This foundational ticket focuses on creating that dataset. |
| | **Acceptance Criteria:** \<br\> - A dataset of at least 10,000 images of individual chess pieces is collected. \<br\> - The dataset is diverse, including images from the default themes of Lichess and Chess.com, as well as other popular themes. \<br\> - The images are correctly labeled into 13 classes (wP, wN, ..., bK, empty). \<br\> - Data augmentation techniques (e.g., slight rotation, brightness changes) are applied to increase the dataset's robustness. |
| | **Implementation Suggestions:** Start with publicly available datasets (e.g., from Kaggle, Roboflow) and supplement them with custom-scraped images. |
| **GUA-30** | **Build and Train CNN for Piece Classification** |
| | **Background:** This ticket involves the core machine learning task of building and training the Convolutional Neural Network that will classify the pieces. |
| | **Acceptance Criteria:** \<br\> - A CNN architecture is defined (e.g., a lightweight model like MobileNet or a custom architecture). \<br\> - The model is trained on the dataset from GUA-26 until it achieves \>99% validation accuracy. \<br\> - The final trained model is saved in a format suitable for inference in a JavaScript environment (e.g., ONNX, TensorFlow.js format). |
| | **Implementation Suggestions:** Use a Python-based ML framework like PyTorch or TensorFlow for training. This task can be performed offline, separate from the Electron application development. |
| **GUA-31** | **Integrate CNN Model and Reconstruct FEN String** |
| | **Background:** Once the model is trained, it must be integrated into the desktop application to perform live inference on the 64 square images. The results are then assembled into the final FEN string. |
| | **Acceptance Criteria:** \<br\> - The trained model is loaded into the Electron application. \<br\> - A function performs inference on the 64 square images (from GUA-25), yielding an 8x8 grid of classifications. \<br\> - A second function traverses this grid and correctly assembles the piece placement part of a FEN string. \<br\> - The final FEN string is successfully passed to the backend service built in Phase 1 for analysis. |
| | **Implementation Suggestions:** Use a library like `ONNX Runtime` or `TensorFlow.js` for performing inference within the JavaScript environment of Electron. |