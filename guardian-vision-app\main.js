const { app, BrowserWindow, ipcMain, desktopCapturer } = require('electron');
const path = require('path');
const fs = require('fs');

function createWindow() {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });
  win.loadFile('index.html');
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

ipcMain.on('ping', (event) => {
  console.log('Ping received from renderer!');
});

ipcMain.handle('get-sources', async (event) => {
  const sources = await desktopCapturer.getSources({ types: ['window', 'screen'] });
  // Only send necessary info to renderer
  return sources.map(source => ({
    id: source.id,
    name: source.name,
    thumbnail: source.thumbnail.toDataURL()
  }));
});

ipcMain.on('captured-image', (event, data) => {
  const buffer = Buffer.from(data);
  fs.writeFile('capture.png', buffer, err => {
    if (err) {
      console.error('ERROR: Failed to save capture.png:', err);
    } else {
      console.log('INFO: Screen region captured and saved to capture.png');
    }
  });
});

// Handle unwarped board image saving
ipcMain.on('save-unwarped-board', (event, data) => {
  const buffer = Buffer.from(data);
  fs.writeFile('unwarped_board.png', buffer, err => {
    if (err) {
      console.error('ERROR: Failed to save unwarped_board.png:', err);
    } else {
      console.log('INFO: Unwarped board saved to unwarped_board.png');
    }
  });
});