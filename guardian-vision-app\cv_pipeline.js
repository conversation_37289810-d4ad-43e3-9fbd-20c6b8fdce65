/**
 * Guardian Vision App - Computer Vision Pipeline
 * Robust chessboard detection and perspective correction using OpenCV.js
 * 
 * Research-driven implementation with multiple detection strategies:
 * 1. Contour-based detection with shape analysis
 * 2. Line-based detection using Hough transforms
 * 3. Corner detection with Harris and FAST algorithms
 * 4. Automatic parameter tuning and fallback mechanisms
 */

// Global variables for debugging and visualization
let debugCanvas, debugCtx, detectedCanvas, detectedCtx;
let lastDetectionResult = null;

// Detection parameters - automatically tuned based on image characteristics
const DETECTION_PARAMS = {
    // Preprocessing
    gaussianBlurSize: 5,
    gaussianSigma: 1.0,
    
    // Canny edge detection - will be auto-tuned
    cannyLow: 50,
    cannyHigh: 150,
    
    // Contour detection
    minContourArea: 1000,
    maxContourArea: 500000,
    approxEpsilon: 0.02, // 2% of perimeter for polygon approximation
    
    // Shape validation
    minAspectRatio: 0.7,  // Minimum width/height ratio for square detection
    maxAspectRatio: 1.3,  // Maximum width/height ratio for square detection
    minConvexity: 0.85,   // Minimum convexity for valid board shape
    
    // Perspective correction
    outputSize: 400,      // Size of the unwarped board image
    
    // Hough line detection (fallback method)
    houghRho: 1,
    houghTheta: Math.PI / 180,
    houghThreshold: 100,
    houghMinLineLength: 50,
    houghMaxLineGap: 10
};

/**
 * Initialize the computer vision pipeline
 */
function initializeCVPipeline() {
    debugCanvas = document.getElementById('debug-canvas');
    debugCtx = debugCanvas.getContext('2d');
    detectedCanvas = document.getElementById('detected-board');
    detectedCtx = detectedCanvas.getContext('2d');
    
    console.log('CV Pipeline initialized');
}

/**
 * Main function to process captured image and detect chessboard
 * @param {HTMLCanvasElement} inputCanvas - Canvas containing the captured image
 */
function processImageForBoard(inputCanvas) {
    console.log('processImageForBoard called');

    if (!cv || !cv.Mat) {
        console.error('OpenCV.js not ready');
        updateStatus('OpenCV.js not ready - please wait for loading');
        return;
    }

    console.log('OpenCV.js is ready, starting detection');

    try {
        updateStatus('Processing image for board detection...');

        // Convert canvas to OpenCV Mat
        const src = cv.imread(inputCanvas);
        console.log(`Image loaded: ${src.cols}x${src.rows} pixels`);

        // Auto-tune parameters based on image characteristics
        autoTuneParameters(src);

        // Try multiple detection strategies
        let boardCorners = null;
        let detectionMethod = 'none';

        console.log('Trying contour-based detection...');
        // Strategy 1: Contour-based detection (primary method)
        boardCorners = detectBoardByContours(src);
        if (boardCorners) {
            detectionMethod = 'contours';
            console.log('Contour detection successful');
        } else {
            console.log('Contour detection failed, trying line-based detection...');
            // Strategy 2: Line-based detection (fallback)
            boardCorners = detectBoardByLines(src);
            if (boardCorners) {
                detectionMethod = 'lines';
                console.log('Line detection successful');
            } else {
                console.log('Line detection failed, trying corner detection...');
                // Strategy 3: Corner detection (last resort)
                boardCorners = detectBoardByCorners(src);
                if (boardCorners) {
                    detectionMethod = 'corners';
                    console.log('Corner detection successful');
                } else {
                    console.log('Corner detection failed, trying simple rectangle detection...');
                    // Strategy 4: Simple rectangle detection (final fallback)
                    boardCorners = detectLargestRectangle(src);
                    if (boardCorners) {
                        detectionMethod = 'simple-rectangle';
                        console.log('Simple rectangle detection successful');
                    }
                }
            }
        }

        if (boardCorners) {
            console.log('Board corners found:', boardCorners);
            // Validate and refine the detected corners
            boardCorners = validateAndRefineCorners(src, boardCorners);

            if (boardCorners) {
                console.log('Corners validated, performing perspective correction...');
                // Perform perspective correction
                const unwarpedBoard = unwarpBoard(src, boardCorners);

                // Display results
                displayResults(src, boardCorners, unwarpedBoard, detectionMethod);

                // Save the unwarped board
                saveUnwarpedBoard(unwarpedBoard);

                unwarpedBoard.delete();
                updateStatus(`Board detected successfully using ${detectionMethod} method`);
            } else {
                console.log('Board corners validation failed');
                updateStatus('Board corners validation failed');
                displayDebugInfo(src, 'Corners validation failed');
            }
        } else {
            console.log('No board detected with any method');
            updateStatus('No chessboard detected - try adjusting the view or lighting');
            displayDebugInfo(src, 'No board detected');
        }

        src.delete();

    } catch (error) {
        console.error('Error in board detection:', error);
        updateStatus(`Error: ${error.message}`);
        displayDebugInfo(null, `Error: ${error.message}`);
    }
}

/**
 * Auto-tune detection parameters based on image characteristics
 * @param {cv.Mat} src - Source image
 */
function autoTuneParameters(src) {
    // Calculate image statistics for parameter tuning
    const gray = new cv.Mat();
    cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);
    
    // Calculate mean brightness
    const mean = cv.mean(gray);
    const brightness = mean[0];
    
    // Calculate contrast (standard deviation)
    const meanMat = new cv.Mat(gray.rows, gray.cols, cv.CV_8UC1, new cv.Scalar(brightness));
    const diff = new cv.Mat();
    cv.subtract(gray, meanMat, diff);
    cv.multiply(diff, diff, diff);
    const variance = cv.mean(diff)[0];
    const contrast = Math.sqrt(variance);
    
    // Adjust Canny thresholds based on image characteristics
    if (brightness < 100) {
        // Dark image - lower thresholds
        DETECTION_PARAMS.cannyLow = 30;
        DETECTION_PARAMS.cannyHigh = 100;
    } else if (brightness > 180) {
        // Bright image - higher thresholds
        DETECTION_PARAMS.cannyLow = 80;
        DETECTION_PARAMS.cannyHigh = 200;
    } else {
        // Normal brightness
        DETECTION_PARAMS.cannyLow = 50;
        DETECTION_PARAMS.cannyHigh = 150;
    }
    
    // Adjust based on contrast
    if (contrast < 30) {
        // Low contrast - more sensitive detection
        DETECTION_PARAMS.cannyLow *= 0.7;
        DETECTION_PARAMS.cannyHigh *= 0.7;
        DETECTION_PARAMS.approxEpsilon = 0.03; // More flexible shape approximation
    } else if (contrast > 80) {
        // High contrast - less sensitive detection
        DETECTION_PARAMS.cannyLow *= 1.3;
        DETECTION_PARAMS.cannyHigh *= 1.3;
        DETECTION_PARAMS.approxEpsilon = 0.015; // Stricter shape approximation
    }
    
    // Adjust contour area based on image size
    const imageArea = src.rows * src.cols;
    // Chessboard should be a significant portion of the image - at least 10% for full board detection
    DETECTION_PARAMS.minContourArea = imageArea * 0.10; // At least 10% of image (much larger than individual squares)
    DETECTION_PARAMS.maxContourArea = imageArea * 0.8;  // At most 80% of image

    console.log(`Auto-tuned area thresholds: min=${DETECTION_PARAMS.minContourArea}, max=${DETECTION_PARAMS.maxContourArea} (image area: ${imageArea})`);
    
    // Clean up
    gray.delete();
    meanMat.delete();
    diff.delete();
    
    console.log('Auto-tuned parameters:', {
        brightness: brightness.toFixed(1),
        contrast: contrast.toFixed(1),
        cannyLow: DETECTION_PARAMS.cannyLow,
        cannyHigh: DETECTION_PARAMS.cannyHigh
    });
}

/**
 * Strategy 1: Detect chessboard using contour analysis
 * @param {cv.Mat} src - Source image
 * @returns {Array|null} - Array of 4 corner points or null if not found
 */
function detectBoardByContours(src) {
    const gray = new cv.Mat();
    const blurred = new cv.Mat();
    const edges = new cv.Mat();
    const contours = new cv.MatVector();
    const hierarchy = new cv.Mat();

    try {
        console.log('Starting contour-based detection...');

        // Preprocessing
        cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);
        cv.GaussianBlur(gray, blurred, new cv.Size(DETECTION_PARAMS.gaussianBlurSize, DETECTION_PARAMS.gaussianBlurSize),
                       DETECTION_PARAMS.gaussianSigma);
        console.log('Preprocessing complete');

        // Edge detection
        cv.Canny(blurred, edges, DETECTION_PARAMS.cannyLow, DETECTION_PARAMS.cannyHigh);
        console.log(`Edge detection complete with thresholds: ${DETECTION_PARAMS.cannyLow}, ${DETECTION_PARAMS.cannyHigh}`);

        // Find contours
        cv.findContours(edges, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
        console.log(`Found ${contours.size()} contours`);

        // Analyze contours to find the best chessboard candidate
        let bestCandidate = null;
        let bestScore = 0;
        let candidatesEvaluated = 0;

        for (let i = 0; i < contours.size(); i++) {
            const contour = contours.get(i);
            const area = cv.contourArea(contour);

            // Filter by area - use strict thresholds to ensure we get full board, not individual squares
            const minArea = DETECTION_PARAMS.minContourArea; // No leniency - we want large contours only
            const maxArea = DETECTION_PARAMS.maxContourArea;

            if (area < minArea || area > maxArea) {
                continue;
            }

            console.log(`Contour ${i}: area = ${area.toFixed(0)} (range: ${minArea.toFixed(0)} - ${maxArea.toFixed(0)})`);

            // Approximate contour to polygon
            const approx = new cv.Mat();
            const perimeter = cv.arcLength(contour, true);
            cv.approxPolyDP(contour, approx, DETECTION_PARAMS.approxEpsilon * perimeter, true);

            console.log(`Contour ${i}: approximated to ${approx.rows} points`);

            // Check if it's a quadrilateral (be more flexible)
            if (approx.rows >= 4 && approx.rows <= 8) { // Allow more points
                const corners = getCornerPoints(approx);

                // If more than 4 points, select the best 4
                let finalCorners = corners;
                if (corners.length > 4) {
                    finalCorners = selectMostSeparatedPoints(corners, 4);
                }

                candidatesEvaluated++;
                const score = evaluateChessboardCandidate(finalCorners, area);
                console.log(`Candidate ${candidatesEvaluated}: score = ${score.toFixed(1)}`);

                if (score > bestScore) {
                    bestScore = score;
                    bestCandidate = finalCorners;
                    console.log(`New best candidate with score ${score.toFixed(1)}`);
                }
            }

            approx.delete();
        }

        console.log(`Contour detection complete: ${candidatesEvaluated} candidates evaluated, best score: ${bestScore.toFixed(1)}`);

        // Visualize the detection process
        visualizeContourDetection(src, edges, contours, bestCandidate);

        // Lower threshold for acceptance
        const threshold = 20; // Much lower threshold
        return bestCandidate && bestScore > threshold ? bestCandidate : null;

    } finally {
        // Clean up
        gray.delete();
        blurred.delete();
        edges.delete();
        contours.delete();
        hierarchy.delete();
    }
}

/**
 * Extract corner points from approximated contour
 * @param {cv.Mat} approx - Approximated contour
 * @returns {Array} - Array of corner points
 */
function getCornerPoints(approx) {
    const corners = [];
    for (let i = 0; i < approx.rows; i++) {
        const point = approx.data32S.slice(i * 2, i * 2 + 2);
        corners.push({ x: point[0], y: point[1] });
    }
    return corners;
}

/**
 * Evaluate how likely a set of corners represents a chessboard
 * @param {Array} corners - Array of 4 corner points
 * @param {number} area - Contour area
 * @returns {number} - Score (higher is better)
 */
function evaluateChessboardCandidate(corners, area) {
    if (corners.length !== 4) return 0;

    try {
        // Calculate side lengths
        const sides = [];
        for (let i = 0; i < 4; i++) {
            const p1 = corners[i];
            const p2 = corners[(i + 1) % 4];
            const length = Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2);
            sides.push(length);
        }

        // Check aspect ratio (should be close to square) - be more lenient
        const minSide = Math.min(...sides);
        const maxSide = Math.max(...sides);
        const aspectRatio = minSide / maxSide;

        // Much more lenient aspect ratio check
        if (aspectRatio < 0.3) return 0; // Very lenient minimum

        // Calculate angles between sides
        const angles = [];
        for (let i = 0; i < 4; i++) {
            const p1 = corners[i];
            const p2 = corners[(i + 1) % 4];
            const p3 = corners[(i + 2) % 4];

            const v1 = { x: p1.x - p2.x, y: p1.y - p2.y };
            const v2 = { x: p3.x - p2.x, y: p3.y - p2.y };

            const dot = v1.x * v2.x + v1.y * v2.y;
            const mag1 = Math.sqrt(v1.x ** 2 + v1.y ** 2);
            const mag2 = Math.sqrt(v2.x ** 2 + v2.y ** 2);

            if (mag1 > 0 && mag2 > 0) {
                const cosAngle = Math.max(-1, Math.min(1, dot / (mag1 * mag2))); // Clamp to avoid NaN
                const angle = Math.acos(cosAngle) * 180 / Math.PI;
                angles.push(angle);
            } else {
                angles.push(90); // Default to 90 if calculation fails
            }
        }

        // Check if angles are close to 90 degrees - be more lenient
        const angleDeviation = angles.reduce((sum, angle) => sum + Math.abs(angle - 90), 0) / 4;

        // Calculate score based on multiple factors - heavily favor larger areas
        let score = 0;
        score += aspectRatio * 40; // Aspect ratio component
        score += Math.max(0, 60 - angleDeviation); // Angle component

        // Area scoring - heavily favor larger contours (full boards over individual squares)
        // Scale area score to strongly prefer larger areas
        const areaScore = Math.min(area / 10000, 100); // Much higher weight for area
        score += areaScore;

        // Bonus for very large areas (likely full boards)
        if (area > 50000) {
            score += 50; // Significant bonus for large contours
        }

        console.log(`Candidate: aspect=${aspectRatio.toFixed(2)}, avgAngleDev=${angleDeviation.toFixed(1)}, area=${area.toFixed(0)}, areaScore=${areaScore.toFixed(1)}, totalScore=${score.toFixed(1)}`);

        return score;

    } catch (error) {
        console.error('Error evaluating candidate:', error);
        return 0;
    }
}

/**
 * Strategy 2: Detect chessboard using line detection (Hough transform)
 * @param {cv.Mat} src - Source image
 * @returns {Array|null} - Array of 4 corner points or null if not found
 */
function detectBoardByLines(src) {
    const gray = new cv.Mat();
    const blurred = new cv.Mat();
    const edges = new cv.Mat();
    const lines = new cv.Mat();

    try {
        // Preprocessing
        cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);
        cv.GaussianBlur(gray, blurred, new cv.Size(5, 5), 1.0);
        cv.Canny(blurred, edges, DETECTION_PARAMS.cannyLow, DETECTION_PARAMS.cannyHigh);

        // Detect lines using Hough transform
        if (typeof cv.HoughLinesP !== 'function') {
            console.log('HoughLinesP not available in this OpenCV.js build');
            return null;
        }
        cv.HoughLinesP(edges, lines, DETECTION_PARAMS.houghRho, DETECTION_PARAMS.houghTheta,
                      DETECTION_PARAMS.houghThreshold, DETECTION_PARAMS.houghMinLineLength,
                      DETECTION_PARAMS.houghMaxLineGap);

        // Group lines into horizontal and vertical
        const horizontalLines = [];
        const verticalLines = [];

        for (let i = 0; i < lines.rows; i++) {
            const line = lines.data32S.slice(i * 4, i * 4 + 4);
            const [x1, y1, x2, y2] = line;

            const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
            const absAngle = Math.abs(angle);

            if (absAngle < 15 || absAngle > 165) {
                horizontalLines.push({ x1, y1, x2, y2, angle });
            } else if (absAngle > 75 && absAngle < 105) {
                verticalLines.push({ x1, y1, x2, y2, angle });
            }
        }

        // Find intersections to form corners
        const corners = findLineIntersections(horizontalLines, verticalLines);

        if (corners && corners.length === 4) {
            // Sort corners to form a proper quadrilateral
            const sortedCorners = sortCorners(corners);
            visualizeLineDetection(src, edges, horizontalLines, verticalLines, sortedCorners);
            return sortedCorners;
        }

        return null;

    } finally {
        gray.delete();
        blurred.delete();
        edges.delete();
        lines.delete();
    }
}

/**
 * Strategy 3: Detect chessboard using corner detection
 * @param {cv.Mat} src - Source image
 * @returns {Array|null} - Array of 4 corner points or null if not found
 */
function detectBoardByCorners(src) {
    const gray = new cv.Mat();
    const corners = new cv.Mat();

    try {
        cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);

        // Harris corner detection
        if (typeof cv.cornerHarris !== 'function') {
            console.log('cornerHarris not available in this OpenCV.js build');
            return null;
        }
        cv.cornerHarris(gray, corners, 2, 3, 0.04);

        // Find strong corners
        const threshold = 0.01;
        const cornerPoints = [];

        for (let i = 0; i < corners.rows; i++) {
            for (let j = 0; j < corners.cols; j++) {
                const value = corners.floatPtr(i, j)[0];
                if (value > threshold) {
                    cornerPoints.push({ x: j, y: i, strength: value });
                }
            }
        }

        // Sort by strength and take top corners
        cornerPoints.sort((a, b) => b.strength - a.strength);

        if (cornerPoints.length >= 4) {
            // Try to find 4 corners that form a reasonable quadrilateral
            const selectedCorners = selectBestCornerQuad(cornerPoints.slice(0, 20));
            if (selectedCorners) {
                visualizeCornerDetection(src, cornerPoints, selectedCorners);
                return selectedCorners;
            }
        }

        return null;

    } finally {
        gray.delete();
        corners.delete();
    }
}

/**
 * Strategy 4: Simple rectangle detection (fallback)
 * @param {cv.Mat} src - Source image
 * @returns {Array|null} - Array of 4 corner points or null if not found
 */
function detectLargestRectangle(src) {
    console.log('Starting simple rectangle detection...');

    const gray = new cv.Mat();
    const edges = new cv.Mat();
    const contours = new cv.MatVector();
    const hierarchy = new cv.Mat();

    try {
        // Convert to grayscale
        cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);

        // Very simple edge detection with low thresholds
        cv.Canny(gray, edges, 30, 90);

        // Find contours
        cv.findContours(edges, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
        console.log(`Simple detection found ${contours.size()} contours`);

        let largestArea = 0;
        let bestCorners = null;

        for (let i = 0; i < contours.size(); i++) {
            const contour = contours.get(i);
            const area = cv.contourArea(contour);

            // Very lenient area filter - just needs to be reasonably sized
            if (area > 1000) {
                console.log(`Simple detection: contour ${i} area = ${area.toFixed(0)}`);

                // Try to approximate to a polygon
                const approx = new cv.Mat();
                const perimeter = cv.arcLength(contour, true);
                cv.approxPolyDP(contour, approx, 0.05 * perimeter, true); // Very lenient approximation

                console.log(`Simple detection: contour ${i} approximated to ${approx.rows} points`);

                // Accept anything with 4-8 points
                if (approx.rows >= 4 && approx.rows <= 8 && area > largestArea) {
                    const corners = getCornerPoints(approx);

                    // If more than 4 points, select the most separated ones
                    let finalCorners = corners;
                    if (corners.length > 4) {
                        finalCorners = selectMostSeparatedPoints(corners, 4);
                    }

                    if (finalCorners.length === 4) {
                        largestArea = area;
                        bestCorners = finalCorners;
                        console.log(`Simple detection: new best candidate with area ${area.toFixed(0)}`);
                    }
                }

                approx.delete();
            }
        }

        if (bestCorners) {
            console.log('Simple rectangle detection successful');
            return bestCorners;
        }

        console.log('Simple rectangle detection failed');
        return null;

    } finally {
        gray.delete();
        edges.delete();
        contours.delete();
        hierarchy.delete();
    }
}

/**
 * Find intersections between horizontal and vertical lines
 * @param {Array} horizontalLines - Array of horizontal lines
 * @param {Array} verticalLines - Array of vertical lines
 * @returns {Array|null} - Array of intersection points
 */
function findLineIntersections(horizontalLines, verticalLines) {
    const intersections = [];

    for (const hLine of horizontalLines) {
        for (const vLine of verticalLines) {
            const intersection = getLineIntersection(hLine, vLine);
            if (intersection) {
                intersections.push(intersection);
            }
        }
    }

    if (intersections.length < 4) return null;

    // Cluster nearby intersections and find the 4 main corners
    const clustered = clusterPoints(intersections, 20);

    if (clustered.length >= 4) {
        // Return the 4 most separated points
        return selectMostSeparatedPoints(clustered, 4);
    }

    return null;
}

/**
 * Calculate intersection point of two lines
 * @param {Object} line1 - First line {x1, y1, x2, y2}
 * @param {Object} line2 - Second line {x1, y1, x2, y2}
 * @returns {Object|null} - Intersection point {x, y} or null
 */
function getLineIntersection(line1, line2) {
    const { x1: x1a, y1: y1a, x2: x2a, y2: y2a } = line1;
    const { x1: x1b, y1: y1b, x2: x2b, y2: y2b } = line2;

    const denom = (x1a - x2a) * (y1b - y2b) - (y1a - y2a) * (x1b - x2b);
    if (Math.abs(denom) < 1e-10) return null; // Lines are parallel

    const t = ((x1a - x1b) * (y1b - y2b) - (y1a - y1b) * (x1b - x2b)) / denom;

    const x = x1a + t * (x2a - x1a);
    const y = y1a + t * (y2a - y1a);

    return { x, y };
}

/**
 * Sort corners in clockwise order starting from top-left
 * @param {Array} corners - Array of corner points
 * @returns {Array} - Sorted corners
 */
function sortCorners(corners) {
    if (corners.length !== 4) return corners;

    // Find center point
    const centerX = corners.reduce((sum, p) => sum + p.x, 0) / 4;
    const centerY = corners.reduce((sum, p) => sum + p.y, 0) / 4;

    // Sort by angle from center
    const sorted = corners.map(corner => ({
        ...corner,
        angle: Math.atan2(corner.y - centerY, corner.x - centerX)
    })).sort((a, b) => a.angle - b.angle);

    // Find top-left corner (smallest x + y)
    let minSum = Infinity;
    let startIndex = 0;

    for (let i = 0; i < 4; i++) {
        const sum = sorted[i].x + sorted[i].y;
        if (sum < minSum) {
            minSum = sum;
            startIndex = i;
        }
    }

    // Reorder starting from top-left
    const result = [];
    for (let i = 0; i < 4; i++) {
        result.push(sorted[(startIndex + i) % 4]);
    }

    return result;
}

/**
 * Validate and refine detected corners
 * @param {cv.Mat} src - Source image
 * @param {Array} corners - Detected corners
 * @returns {Array|null} - Refined corners or null if invalid
 */
function validateAndRefineCorners(src, corners) {
    if (!corners || corners.length !== 4) return null;

    // Check if corners form a reasonable quadrilateral
    const area = calculateQuadrilateralArea(corners);
    const imageArea = src.rows * src.cols;

    if (area < imageArea * 0.01 || area > imageArea * 0.8) {
        return null; // Area too small or too large
    }

    // Refine corners using sub-pixel accuracy
    const refinedCorners = refineCorners(src, corners);

    return refinedCorners;
}

/**
 * Calculate area of quadrilateral
 * @param {Array} corners - Array of 4 corner points
 * @returns {number} - Area
 */
function calculateQuadrilateralArea(corners) {
    if (corners.length !== 4) return 0;

    // Shoelace formula
    let area = 0;
    for (let i = 0; i < 4; i++) {
        const j = (i + 1) % 4;
        area += corners[i].x * corners[j].y;
        area -= corners[j].x * corners[i].y;
    }
    return Math.abs(area) / 2;
}

/**
 * Refine corner positions using simple validation
 * @param {cv.Mat} src - Source image
 * @param {Array} corners - Initial corner positions
 * @returns {Array} - Refined corners
 */
function refineCorners(src, corners) {
    // Since cornerSubPix is not available in this OpenCV.js build,
    // we'll just validate and return the corners as-is
    console.log('Refining corners (validation only - cornerSubPix not available)');

    // Ensure corners are within image bounds
    const refined = corners.map(corner => ({
        x: Math.max(0, Math.min(src.cols - 1, Math.round(corner.x))),
        y: Math.max(0, Math.min(src.rows - 1, Math.round(corner.y)))
    }));

    console.log('Corner refinement complete');
    return refined;
}

/**
 * Perform perspective correction to unwarp the board
 * @param {cv.Mat} src - Source image
 * @param {Array} corners - Array of 4 corner points
 * @returns {cv.Mat} - Unwarped board image
 */
function unwarpBoard(src, corners) {
    const size = DETECTION_PARAMS.outputSize;

    // Define destination points (perfect square)
    const dstPoints = [
        [0, 0],
        [size - 1, 0],
        [size - 1, size - 1],
        [0, size - 1]
    ];

    // Convert source corners to the format expected by OpenCV
    const srcPoints = corners.map(c => [c.x, c.y]);

    // Create transformation matrix
    const srcMat = cv.matFromArray(4, 1, cv.CV_32FC2, srcPoints.flat());
    const dstMat = cv.matFromArray(4, 1, cv.CV_32FC2, dstPoints.flat());
    const transformMatrix = cv.getPerspectiveTransform(srcMat, dstMat);

    // Apply perspective transformation
    const unwarped = new cv.Mat();
    cv.warpPerspective(src, unwarped, transformMatrix, new cv.Size(size, size));

    // Clean up
    srcMat.delete();
    dstMat.delete();
    transformMatrix.delete();

    return unwarped;
}

/**
 * Display detection results and debug information
 * @param {cv.Mat} src - Source image
 * @param {Array} corners - Detected corners
 * @param {cv.Mat} unwarpedBoard - Unwarped board image
 * @param {string} method - Detection method used
 */
function displayResults(src, corners, unwarpedBoard, method) {
    // Display the unwarped board
    cv.imshow('detected-board', unwarpedBoard);

    // Update detection info
    const info = document.getElementById('detection-info');
    info.innerHTML = `
        <strong>Detection Method:</strong> ${method}<br>
        <strong>Board Size:</strong> ${DETECTION_PARAMS.outputSize}x${DETECTION_PARAMS.outputSize}<br>
        <strong>Corners:</strong><br>
        ${corners.map((c, i) => `  ${i + 1}: (${c.x.toFixed(1)}, ${c.y.toFixed(1)})`).join('<br>')}
    `;

    // Draw corners on debug canvas
    const debugImg = src.clone();
    drawCorners(debugImg, corners);
    cv.imshow('debug-canvas', debugImg);
    debugImg.delete();

    // Update debug info
    const debugInfo = document.getElementById('debug-info');
    debugInfo.innerHTML = `
        <strong>Image Size:</strong> ${src.cols}x${src.rows}<br>
        <strong>Detection Parameters:</strong><br>
        Canny: ${DETECTION_PARAMS.cannyLow}-${DETECTION_PARAMS.cannyHigh}<br>
        Min Area: ${DETECTION_PARAMS.minContourArea.toFixed(0)}<br>
        Approx ε: ${DETECTION_PARAMS.approxEpsilon}
    `;
}

/**
 * Draw corners on image for visualization
 * @param {cv.Mat} img - Image to draw on
 * @param {Array} corners - Corner points
 */
function drawCorners(img, corners) {
    if (!corners || corners.length !== 4) return;

    // Draw corner points
    for (let i = 0; i < corners.length; i++) {
        const point = new cv.Point(Math.round(corners[i].x), Math.round(corners[i].y));
        cv.circle(img, point, 8, new cv.Scalar(0, 255, 0, 255), -1);
        cv.putText(img, `${i + 1}`, new cv.Point(point.x + 10, point.y - 10),
                  cv.FONT_HERSHEY_SIMPLEX, 0.7, new cv.Scalar(0, 255, 0, 255), 2);
    }

    // Draw connecting lines
    for (let i = 0; i < corners.length; i++) {
        const p1 = new cv.Point(Math.round(corners[i].x), Math.round(corners[i].y));
        const p2 = new cv.Point(Math.round(corners[(i + 1) % 4].x), Math.round(corners[(i + 1) % 4].y));
        cv.line(img, p1, p2, new cv.Scalar(255, 0, 0, 255), 3);
    }
}

/**
 * Save the unwarped board image to file
 * @param {cv.Mat} unwarpedBoard - Unwarped board image
 */
function saveUnwarpedBoard(unwarpedBoard) {
    // Convert to canvas and save
    const canvas = document.createElement('canvas');
    cv.imshow(canvas, unwarpedBoard);

    canvas.toBlob(blob => {
        if (blob) {
            blob.arrayBuffer().then(buf => {
                // Send to main process for saving
                if (window.electronAPI && window.electronAPI.saveUnwarpedBoard) {
                    window.electronAPI.saveUnwarpedBoard(new Uint8Array(buf));
                    console.log('Unwarped board sent to main process for saving');
                }
            });
        }
    }, 'image/png');
}

/**
 * Update status message
 * @param {string} message - Status message
 */
function updateStatus(message) {
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = message;
    }
    console.log('CV Status:', message);
}

/**
 * Display debug information when no board is detected
 * @param {cv.Mat} src - Source image
 * @param {string} message - Debug message
 */
function displayDebugInfo(src, message) {
    // Show the original image on debug canvas
    cv.imshow('debug-canvas', src);

    const debugInfo = document.getElementById('debug-info');
    debugInfo.innerHTML = `
        <strong>Status:</strong> ${message}<br>
        <strong>Image Size:</strong> ${src.cols}x${src.rows}<br>
        <strong>Suggestions:</strong><br>
        • Ensure good lighting<br>
        • Make sure board is fully visible<br>
        • Try different camera angle<br>
        • Check for reflections or shadows
    `;
}

/**
 * Visualize contour detection process
 * @param {cv.Mat} src - Source image
 * @param {cv.Mat} edges - Edge image
 * @param {cv.MatVector} contours - Detected contours
 * @param {Array} bestCandidate - Best corner candidate
 */
function visualizeContourDetection(src, edges, contours, bestCandidate) {
    const debugImg = src.clone();

    // Draw all contours in blue
    for (let i = 0; i < contours.size(); i++) {
        cv.drawContours(debugImg, contours, i, new cv.Scalar(255, 0, 0, 255), 2);
    }

    // Draw best candidate in green
    if (bestCandidate) {
        drawCorners(debugImg, bestCandidate);
    }

    cv.imshow('debug-canvas', debugImg);
    debugImg.delete();
}

/**
 * Visualize line detection process
 * @param {cv.Mat} src - Source image
 * @param {cv.Mat} edges - Edge image
 * @param {Array} horizontalLines - Horizontal lines
 * @param {Array} verticalLines - Vertical lines
 * @param {Array} corners - Detected corners
 */
function visualizeLineDetection(src, edges, horizontalLines, verticalLines, corners) {
    const debugImg = src.clone();

    // Draw horizontal lines in red
    for (const line of horizontalLines) {
        cv.line(debugImg,
               new cv.Point(line.x1, line.y1),
               new cv.Point(line.x2, line.y2),
               new cv.Scalar(0, 0, 255, 255), 2);
    }

    // Draw vertical lines in blue
    for (const line of verticalLines) {
        cv.line(debugImg,
               new cv.Point(line.x1, line.y1),
               new cv.Point(line.x2, line.y2),
               new cv.Scalar(255, 0, 0, 255), 2);
    }

    // Draw corners
    if (corners) {
        drawCorners(debugImg, corners);
    }

    cv.imshow('debug-canvas', debugImg);
    debugImg.delete();
}

/**
 * Visualize corner detection process
 * @param {cv.Mat} src - Source image
 * @param {Array} allCorners - All detected corners
 * @param {Array} selectedCorners - Selected corners
 */
function visualizeCornerDetection(src, allCorners, selectedCorners) {
    const debugImg = src.clone();

    // Draw all corners in blue
    for (const corner of allCorners.slice(0, 50)) { // Limit to avoid clutter
        cv.circle(debugImg, new cv.Point(corner.x, corner.y), 3, new cv.Scalar(255, 0, 0, 255), -1);
    }

    // Draw selected corners
    if (selectedCorners) {
        drawCorners(debugImg, selectedCorners);
    }

    cv.imshow('debug-canvas', debugImg);
    debugImg.delete();
}

/**
 * Cluster nearby points
 * @param {Array} points - Array of points
 * @param {number} threshold - Distance threshold for clustering
 * @returns {Array} - Array of cluster centers
 */
function clusterPoints(points, threshold) {
    const clusters = [];
    const used = new Set();

    for (let i = 0; i < points.length; i++) {
        if (used.has(i)) continue;

        const cluster = [points[i]];
        used.add(i);

        for (let j = i + 1; j < points.length; j++) {
            if (used.has(j)) continue;

            const dist = Math.sqrt(
                (points[i].x - points[j].x) ** 2 +
                (points[i].y - points[j].y) ** 2
            );

            if (dist < threshold) {
                cluster.push(points[j]);
                used.add(j);
            }
        }

        // Calculate cluster center
        const center = {
            x: cluster.reduce((sum, p) => sum + p.x, 0) / cluster.length,
            y: cluster.reduce((sum, p) => sum + p.y, 0) / cluster.length
        };

        clusters.push(center);
    }

    return clusters;
}

/**
 * Select the most separated points from a set
 * @param {Array} points - Array of points
 * @param {number} count - Number of points to select
 * @returns {Array} - Selected points
 */
function selectMostSeparatedPoints(points, count) {
    if (points.length <= count) return points;

    const selected = [points[0]]; // Start with first point

    for (let i = 1; i < count; i++) {
        let maxMinDist = 0;
        let bestPoint = null;

        for (const point of points) {
            if (selected.includes(point)) continue;

            // Find minimum distance to already selected points
            let minDist = Infinity;
            for (const selPoint of selected) {
                const dist = Math.sqrt(
                    (point.x - selPoint.x) ** 2 +
                    (point.y - selPoint.y) ** 2
                );
                minDist = Math.min(minDist, dist);
            }

            if (minDist > maxMinDist) {
                maxMinDist = minDist;
                bestPoint = point;
            }
        }

        if (bestPoint) {
            selected.push(bestPoint);
        }
    }

    return selected;
}

/**
 * Select best quadrilateral from corner points
 * @param {Array} corners - Array of corner points
 * @returns {Array|null} - Best 4 corners or null
 */
function selectBestCornerQuad(corners) {
    if (corners.length < 4) return null;

    // Try different combinations of 4 corners
    let bestQuad = null;
    let bestScore = 0;

    for (let i = 0; i < corners.length - 3; i++) {
        for (let j = i + 1; j < corners.length - 2; j++) {
            for (let k = j + 1; k < corners.length - 1; k++) {
                for (let l = k + 1; l < corners.length; l++) {
                    const quad = [corners[i], corners[j], corners[k], corners[l]];
                    const area = calculateQuadrilateralArea(quad);
                    const score = evaluateChessboardCandidate(quad, area);

                    if (score > bestScore) {
                        bestScore = score;
                        bestQuad = quad;
                    }
                }
            }
        }
    }

    return bestQuad && bestScore > 50 ? sortCorners(bestQuad) : null;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeCVPipeline);
