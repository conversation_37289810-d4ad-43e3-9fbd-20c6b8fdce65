<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Pipeline Test</title>
    <script async src="https://docs.opencv.org/4.x/opencv.js" onload="onOpenCvReady();" type="text/javascript"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        canvas { border: 2px solid #333; margin: 10px; }
        .results { display: flex; gap: 20px; flex-wrap: wrap; }
        #status { padding: 10px; background: #f0f0f0; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Computer Vision Pipeline Test</h1>
    
    <div id="status">Loading OpenCV.js...</div>
    
    <div class="test-section">
        <h2>Test Image</h2>
        <canvas id="test-input" width="400" height="400"></canvas>
        <button onclick="createTestChessboard()">Create Test Chessboard</button>
        <button onclick="testDetection()">Test Detection</button>
    </div>
    
    <div class="test-section">
        <h2>Detection Results</h2>
        <div class="results">
            <div>
                <h3>Debug View</h3>
                <canvas id="debug-canvas" width="400" height="400"></canvas>
            </div>
            <div>
                <h3>Detected Board</h3>
                <canvas id="detected-board" width="400" height="400"></canvas>
            </div>
        </div>
        <div id="detection-info" style="margin-top: 10px; font-family: monospace; background: #f8f8f8; padding: 10px;">
            No detection performed yet
        </div>
    </div>

    <script src="cv_pipeline.js"></script>
    <script>
        let cvReady = false;
        
        function onOpenCvReady() {
            cvReady = true;
            document.getElementById('status').textContent = 'OpenCV.js ready! Click "Create Test Chessboard" to begin.';
            console.log('OpenCV.js is ready for testing');
        }
        
        function createTestChessboard() {
            const canvas = document.getElementById('test-input');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw a simple chessboard pattern
            const boardSize = 300;
            const squareSize = boardSize / 8;
            const offsetX = (canvas.width - boardSize) / 2;
            const offsetY = (canvas.height - boardSize) / 2;
            
            // Add some perspective distortion
            ctx.save();
            ctx.transform(1, 0.1, 0.1, 1, offsetX, offsetY);
            
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 8; col++) {
                    const isBlack = (row + col) % 2 === 1;
                    ctx.fillStyle = isBlack ? '#333' : '#fff';
                    ctx.fillRect(col * squareSize, row * squareSize, squareSize, squareSize);
                }
            }
            
            // Draw border
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 3;
            ctx.strokeRect(0, 0, boardSize, boardSize);
            
            ctx.restore();
            
            document.getElementById('status').textContent = 'Test chessboard created. Click "Test Detection" to analyze.';
        }
        
        function testDetection() {
            if (!cvReady) {
                alert('OpenCV.js not ready yet');
                return;
            }
            
            const canvas = document.getElementById('test-input');
            document.getElementById('status').textContent = 'Running detection...';
            
            try {
                // Process the test image
                processImageForBoard(canvas);
            } catch (error) {
                console.error('Detection test failed:', error);
                document.getElementById('status').textContent = 'Detection failed: ' + error.message;
            }
        }
        
        // Override updateStatus for testing
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('CV Test Status:', message);
        }
    </script>
</body>
</html>
