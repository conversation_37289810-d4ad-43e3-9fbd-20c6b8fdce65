<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Guardian Vision App</title>
  <!-- OpenCV.js -->
  <script async src="https://docs.opencv.org/4.x/opencv.js" onload="onOpenCvReady();" type="text/javascript"></script>
  <style>
    #video-container {
      position: relative;
      display: inline-block;
      margin-top: 20px;
    }
    #preview {
      width: 800px;
      height: 450px;
      background: #222;
      display: block;
    }
    #overlay {
      position: absolute;
      top: 0; left: 0;
      width: 800px;
      height: 450px;
      pointer-events: none;
    }
    #selection {
      position: absolute;
      border: 2px dashed #00ff00;
      background: rgba(0,255,0,0.1);
      display: none;
      pointer-events: none;
    }
    #sources {
      margin-left: 10px;
    }
    #cv-results {
      margin-top: 20px;
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
    }
    .cv-canvas {
      border: 2px solid #333;
      background: #f0f0f0;
    }
    .cv-info {
      font-family: monospace;
      font-size: 12px;
      background: #f8f8f8;
      padding: 10px;
      border: 1px solid #ddd;
      max-width: 300px;
    }
    #status {
      margin-top: 10px;
      padding: 10px;
      background: #e8f4f8;
      border: 1px solid #bee5eb;
      border-radius: 4px;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <h1>Guardian Vision App</h1>
  <button id="selectSourceBtn">Select Window or Screen</button>
  <button id="test-detection-btn">Test Detection</button>
  <select id="sources" style="display:none;"></select>
  <div id="video-container">
    <video id="preview" autoplay muted style="display:none;"></video>
    <div id="overlay">
      <div id="selection"></div>
    </div>
  </div>

  <div id="status">OpenCV.js loading...</div>

  <div id="cv-results">
    <div>
      <h3>Detected Board</h3>
      <canvas id="detected-board" class="cv-canvas" width="400" height="400"></canvas>
      <div id="detection-info" class="cv-info">No board detected yet</div>
    </div>
    <div>
      <h3>Processing Steps</h3>
      <canvas id="debug-canvas" class="cv-canvas" width="400" height="300"></canvas>
      <div id="debug-info" class="cv-info">Waiting for image...</div>
    </div>
  </div>

  <!-- Computer Vision Pipeline -->
  <script src="cv_pipeline.js"></script>

  <script>
    // OpenCV.js initialization
    let cvReady = false;
    function onOpenCvReady() {
      cvReady = true;
      document.getElementById('status').textContent = 'OpenCV.js ready! Select a screen region to start board detection.';
      console.log('OpenCV.js is ready');
    }
  </script>

  <script>
    const selectSourceBtn = document.getElementById('selectSourceBtn');
    const sourcesDropdown = document.getElementById('sources');
    const video = document.getElementById('preview');
    const overlay = document.getElementById('overlay');
    const selection = document.getElementById('selection');

    let stream = null;
    let isSelecting = false;
    let startX = 0, startY = 0, currentX = 0, currentY = 0;
    let captureInterval = null;
    let lastRegion = null;

    selectSourceBtn.addEventListener('click', async () => {
      const sources = await window.electronAPI.getSources();
      sourcesDropdown.innerHTML = '';
      sources.forEach((src, i) => {
        const option = document.createElement('option');
        option.value = src.id;
        option.textContent = src.name;
        sourcesDropdown.appendChild(option);
      });
      sourcesDropdown.style.display = 'inline';
      sourcesDropdown.focus();
      // Auto-select and preview the first source if available
      if (sources.length > 0) {
        sourcesDropdown.value = sources[0].id;
        sourcesDropdown.dispatchEvent(new Event('change'));
      }
    });

    sourcesDropdown.addEventListener('change', async () => {
      const sourceId = sourcesDropdown.value;
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      try {
        stream = await navigator.mediaDevices.getUserMedia({
          audio: false,
          video: {
            mandatory: {
              chromeMediaSource: 'desktop',
              chromeMediaSourceId: sourceId,
              minWidth: 800,
              minHeight: 450,
              maxWidth: 3840,
              maxHeight: 2160
            }
          }
        });
        video.srcObject = stream;
        video.style.display = 'block';
        overlay.style.pointerEvents = 'auto';
      } catch (e) {
        alert('Failed to get video stream: ' + e);
      }
    });

    // Selection logic
    overlay.addEventListener('mousedown', (e) => {
      isSelecting = true;
      const rect = overlay.getBoundingClientRect();
      startX = e.clientX - rect.left;
      startY = e.clientY - rect.top;
      selection.style.left = startX + 'px';
      selection.style.top = startY + 'px';
      selection.style.width = '0px';
      selection.style.height = '0px';
      selection.style.display = 'block';
      overlay.style.pointerEvents = 'auto';
    });
    overlay.addEventListener('mousemove', (e) => {
      if (!isSelecting) return;
      const rect = overlay.getBoundingClientRect();
      currentX = e.clientX - rect.left;
      currentY = e.clientY - rect.top;
      const x = Math.min(startX, currentX);
      const y = Math.min(startY, currentY);
      const w = Math.abs(currentX - startX);
      const h = Math.abs(currentY - startY);
      selection.style.left = x + 'px';
      selection.style.top = y + 'px';
      selection.style.width = w + 'px';
      selection.style.height = h + 'px';
    });
    overlay.addEventListener('mouseup', (e) => {
      if (!isSelecting) return;
      isSelecting = false;
      const rect = overlay.getBoundingClientRect();
      const endX = e.clientX - rect.left;
      const endY = e.clientY - rect.top;
      const x = Math.min(startX, endX);
      const y = Math.min(startY, endY);
      const width = Math.abs(endX - startX);
      const height = Math.abs(endY - startY);
      selection.style.display = 'none';
      overlay.style.pointerEvents = 'none';
      // Save region for capture
      lastRegion = { x, y, width, height };
      window.electronAPI.sendRegion(lastRegion);
      // Start periodic capture
      if (captureInterval) clearInterval(captureInterval);
      captureInterval = setInterval(() => {
        if (!lastRegion || !video.srcObject) return;
        const canvas = document.createElement('canvas');
        const scaleX = video.videoWidth / video.clientWidth;
        const scaleY = video.videoHeight / video.clientHeight;
        const srcX = lastRegion.x * scaleX;
        const srcY = lastRegion.y * scaleY;
        const srcW = lastRegion.width * scaleX;
        const srcH = lastRegion.height * scaleY;
        canvas.width = srcW;
        canvas.height = srcH;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(
          video,
          srcX, srcY, srcW, srcH,
          0, 0, srcW, srcH
        );
        // Process with computer vision pipeline
        if (cvReady && typeof processImageForBoard === 'function') {
          processImageForBoard(canvas);
        }

        // Also send to main process for file saving
        canvas.toBlob(blob => {
          if (blob) {
            blob.arrayBuffer().then(buf => {
              window.electronAPI.sendCapturedImage(new Uint8Array(buf));
            });
          }
        }, 'image/png');
      }, 2000);
    });
    // To allow selection overlay to receive mouse events only when video is visible
    video.addEventListener('play', () => {
      overlay.style.pointerEvents = 'auto';
    });

    // Test detection button
    document.getElementById('test-detection-btn').addEventListener('click', () => {
      console.log('Testing detection with synthetic board...');
      if (!cvReady) {
        alert('OpenCV.js not ready yet');
        return;
      }

      // Check available OpenCV functions
      console.log('OpenCV functions check:');
      console.log('cornerHarris available:', typeof cv.cornerHarris === 'function');
      console.log('cornerSubPix available:', typeof cv.cornerSubPix === 'function');
      console.log('HoughLines available:', typeof cv.HoughLines === 'function');
      console.log('HoughLinesP available:', typeof cv.HoughLinesP === 'function');

      // Create a test canvas with a simple chessboard pattern
      const testCanvas = document.createElement('canvas');
      testCanvas.width = 400;
      testCanvas.height = 400;
      const ctx = testCanvas.getContext('2d');

      // Draw a simple chessboard pattern
      ctx.fillStyle = '#f0d9b5'; // Light squares
      ctx.fillRect(0, 0, 400, 400);

      ctx.fillStyle = '#b58863'; // Dark squares
      for (let row = 0; row < 8; row++) {
        for (let col = 0; col < 8; col++) {
          if ((row + col) % 2 === 1) {
            ctx.fillRect(col * 50, row * 50, 50, 50);
          }
        }
      }

      // Add a border to make it more detectable
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 4;
      ctx.strokeRect(2, 2, 396, 396);

      console.log('Created synthetic chessboard, testing detection...');

      // Test the detection
      if (typeof processImageForBoard === 'function') {
        processImageForBoard(testCanvas);
      } else {
        console.error('processImageForBoard function not available');
      }
    });
  </script>
</body>
</html> 