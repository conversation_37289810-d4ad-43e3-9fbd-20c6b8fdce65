<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Debug Tool</title>
    <script async src="https://docs.opencv.org/4.x/opencv.js" onload="onOpenCvReady();" type="text/javascript"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        canvas { border: 2px solid #333; margin: 10px; }
        .results { display: flex; gap: 20px; flex-wrap: wrap; }
        #status { padding: 10px; background: #f0f0f0; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .log { background: #f8f8f8; padding: 10px; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Computer Vision Debug Tool</h1>
    
    <div id="status">Loading OpenCV.js...</div>
    
    <div class="section">
        <h2>OpenCV.js Status</h2>
        <button onclick="testOpenCV()">Test OpenCV Functions</button>
        <div id="opencv-status" class="log">Not tested yet</div>
    </div>
    
    <div class="section">
        <h2>Test Image</h2>
        <canvas id="test-input" width="400" height="400"></canvas>
        <br>
        <button onclick="createSimpleBoard()">Create Simple Board</button>
        <button onclick="createComplexBoard()">Create Complex Board</button>
        <button onclick="loadCapturedImage()">Load Image File</button>
    </div>
    
    <div class="section">
        <h2>Detection Steps</h2>
        <div class="results">
            <div>
                <h3>Grayscale</h3>
                <canvas id="gray-canvas" width="200" height="200"></canvas>
            </div>
            <div>
                <h3>Edges</h3>
                <canvas id="edges-canvas" width="200" height="200"></canvas>
            </div>
            <div>
                <h3>Contours</h3>
                <canvas id="contours-canvas" width="200" height="200"></canvas>
            </div>
            <div>
                <h3>Result</h3>
                <canvas id="result-canvas" width="200" height="200"></canvas>
            </div>
        </div>
        <button onclick="runStepByStep()">Run Step-by-Step Detection</button>
        <div id="detection-log" class="log">No detection run yet</div>
    </div>

    <script>
        let cvReady = false;
        let testImage = null;
        
        function onOpenCvReady() {
            cvReady = true;
            document.getElementById('status').textContent = 'OpenCV.js ready!';
            console.log('OpenCV.js is ready');
            testOpenCV();
        }
        
        function testOpenCV() {
            const statusDiv = document.getElementById('opencv-status');
            let status = '';
            
            try {
                status += `OpenCV version: ${cv.getBuildInformation().split('\\n')[0]}\\n`;
                status += `cv object exists: ${typeof cv !== 'undefined'}\\n`;
                status += `cv.Mat exists: ${typeof cv.Mat !== 'undefined'}\\n`;
                status += `cv.imread exists: ${typeof cv.imread !== 'undefined'}\\n`;
                status += `cv.cvtColor exists: ${typeof cv.cvtColor !== 'undefined'}\\n`;
                status += `cv.Canny exists: ${typeof cv.Canny !== 'undefined'}\\n`;
                status += `cv.findContours exists: ${typeof cv.findContours !== 'undefined'}\\n`;
                
                // Test basic Mat creation
                const testMat = new cv.Mat(100, 100, cv.CV_8UC3);
                status += `Mat creation: SUCCESS (${testMat.rows}x${testMat.cols})\\n`;
                testMat.delete();
                
                status += 'OpenCV.js is fully functional!';
            } catch (error) {
                status += `ERROR: ${error.message}`;
            }
            
            statusDiv.textContent = status;
        }
        
        function createSimpleBoard() {
            const canvas = document.getElementById('test-input');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw a simple black square (chessboard outline)
            const size = 200;
            const x = (canvas.width - size) / 2;
            const y = (canvas.height - size) / 2;
            
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 5;
            ctx.strokeRect(x, y, size, size);
            
            // Add some internal lines to make it look more like a board
            for (let i = 1; i < 8; i++) {
                const lineX = x + (size / 8) * i;
                const lineY = y + (size / 8) * i;
                ctx.beginPath();
                ctx.moveTo(lineX, y);
                ctx.lineTo(lineX, y + size);
                ctx.moveTo(x, lineY);
                ctx.lineTo(x + size, lineY);
                ctx.stroke();
            }
            
            console.log('Simple board created');
        }
        
        function createComplexBoard() {
            const canvas = document.getElementById('test-input');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw a chessboard pattern
            const boardSize = 240;
            const squareSize = boardSize / 8;
            const offsetX = (canvas.width - boardSize) / 2;
            const offsetY = (canvas.height - boardSize) / 2;
            
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 8; col++) {
                    const isBlack = (row + col) % 2 === 1;
                    ctx.fillStyle = isBlack ? '#333' : '#fff';
                    ctx.fillRect(offsetX + col * squareSize, offsetY + row * squareSize, squareSize, squareSize);
                }
            }
            
            // Draw border
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 3;
            ctx.strokeRect(offsetX, offsetY, boardSize, boardSize);
            
            console.log('Complex board created');
        }
        
        function loadCapturedImage() {
            // Create a file input to avoid CORS issues
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        const img = new Image();
                        img.onload = function() {
                            const canvas = document.getElementById('test-input');
                            const ctx = canvas.getContext('2d');
                            ctx.clearRect(0, 0, canvas.width, canvas.height);
                            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                            console.log('Image loaded from file');
                        };
                        img.src = event.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            };
            fileInput.click();
        }
        
        function runStepByStep() {
            if (!cvReady) {
                alert('OpenCV.js not ready');
                return;
            }
            
            const canvas = document.getElementById('test-input');
            const logDiv = document.getElementById('detection-log');
            let log = '';
            
            try {
                log += 'Starting step-by-step detection...\\n';
                
                // Step 1: Load image
                const src = cv.imread(canvas);
                log += `Image loaded: ${src.cols}x${src.rows} pixels\\n`;
                
                // Step 2: Convert to grayscale
                const gray = new cv.Mat();
                cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);
                cv.imshow('gray-canvas', gray);
                log += 'Converted to grayscale\\n';
                
                // Step 3: Edge detection
                const edges = new cv.Mat();
                cv.Canny(gray, edges, 50, 150);
                cv.imshow('edges-canvas', edges);
                log += 'Edge detection complete\\n';
                
                // Step 4: Find contours
                const contours = new cv.MatVector();
                const hierarchy = new cv.Mat();
                cv.findContours(edges, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
                log += `Found ${contours.size()} contours\\n`;
                
                // Step 5: Draw contours
                const contourImg = src.clone();
                for (let i = 0; i < contours.size(); i++) {
                    const color = new cv.Scalar(Math.random() * 255, Math.random() * 255, Math.random() * 255, 255);
                    cv.drawContours(contourImg, contours, i, color, 2);
                }
                cv.imshow('contours-canvas', contourImg);
                
                // Step 6: Analyze contours
                let bestArea = 0;
                let bestContour = -1;
                for (let i = 0; i < contours.size(); i++) {
                    const area = cv.contourArea(contours.get(i));
                    log += `Contour ${i}: area = ${area.toFixed(0)}\\n`;
                    if (area > bestArea) {
                        bestArea = area;
                        bestContour = i;
                    }
                }
                
                if (bestContour >= 0) {
                    log += `Best contour: ${bestContour} with area ${bestArea.toFixed(0)}\\n`;
                    
                    // Draw best contour
                    const resultImg = src.clone();
                    cv.drawContours(resultImg, contours, bestContour, new cv.Scalar(0, 255, 0, 255), 3);
                    cv.imshow('result-canvas', resultImg);
                    resultImg.delete();
                } else {
                    log += 'No suitable contours found\\n';
                }
                
                // Cleanup
                src.delete();
                gray.delete();
                edges.delete();
                contours.delete();
                hierarchy.delete();
                contourImg.delete();
                
                log += 'Detection complete!';
                
            } catch (error) {
                log += `ERROR: ${error.message}`;
                console.error('Detection error:', error);
            }
            
            logDiv.textContent = log;
        }
    </script>
</body>
</html>
