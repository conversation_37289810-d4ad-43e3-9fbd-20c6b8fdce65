const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  sendMessage: (msg) => ipcRenderer.send(msg),
  getSources: () => ipcRenderer.invoke('get-sources'),
  sendRegion: (region) => ipcRenderer.send('region-selected', region),
  sendCapturedImage: (buffer) => ipcRenderer.send('captured-image', buffer),
  saveUnwarpedBoard: (buffer) => ipcRenderer.send('save-unwarped-board', buffer)
});